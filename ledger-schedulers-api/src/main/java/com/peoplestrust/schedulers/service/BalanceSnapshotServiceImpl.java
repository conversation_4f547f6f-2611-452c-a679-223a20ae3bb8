package com.peoplestrust.schedulers.service;

import com.peoplestrust.account.persistence.entity.AccountEntity;
import com.peoplestrust.account.persistence.repository.read.ReadAccountRepository;
import com.peoplestrust.scheduler.persistence.entity.BalanceEntity;
import com.peoplestrust.scheduler.persistence.entity.MonetaryUnit;
import com.peoplestrust.scheduler.persistence.repository.read.ReadBalanceRepository;
import com.peoplestrust.scheduler.persistence.repository.write.BalanceRepository;
import com.peoplestrust.schedulers.config.SchedulersApiConstant;
import com.peoplestrust.transaction.persistence.repository.read.ReadTransactionRepository;
import com.peoplestrust.util.api.common.config.APICommonUtilConstant;
import com.peoplestrust.util.api.common.mapper.CommonMapper;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.time.OffsetDateTime;
import java.time.ZoneId;
import java.time.ZonedDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;
import lombok.Builder;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.orm.jpa.JpaSystemException;
import org.springframework.stereotype.Service;

@Slf4j
@Service
public class BalanceSnapshotServiceImpl implements BalanceSnapshotService {

  @Autowired
  ReadTransactionRepository readTransactionRepository;

  @Autowired
  ReadAccountRepository readAccountRepository;

  @Autowired
  BalanceRepository balanceRepository;

  @Autowired
  ReadBalanceRepository readBalanceRepository;

  @Override
  public List<BalanceEntity> generateTransactionReBalanceSnapshot() {
    List<BalanceEntity> balanceEntities = new ArrayList<>();
    try {
      balanceEntities = generateBalanceEntities();
    } catch (JpaSystemException e) {
      log.error(SchedulersApiConstant.DATABASE_ERROR + e.getMessage(), e);
    } catch (Exception e) {
      log.error(SchedulersApiConstant.GENERIC_ERROR + e.getMessage(), e);
    }
    return balanceEntities;
  }

  private List<BalanceEntity> generateBalanceEntities() {
    List<BalanceEntity> balanceEntities = new ArrayList<>();
    LocalDateTime now = LocalDateTime.now();
    ZonedDateTime zonedDateTimeET = now.atZone(ZoneId.of(APICommonUtilConstant.AMERICA_TORONTO));
    OffsetDateTime effectiveToDateTimeUTC = CommonMapper.offSetToOffSet(zonedDateTimeET.minusHours(1).toOffsetDateTime());

    // Calculate the actual snapshot run time (current time)
    LocalDateTime snapshotRunTime = now;
    LocalDateTime periodEndTime = effectiveToDateTimeUTC.toLocalDateTime();

    log.info("Starting snapshot generation for effective date up to {} (snapshot run time: {})",
        periodEndTime, snapshotRunTime);

    List<AccountEntity> accounts = readAccountRepository.findAll();
    if (accounts.isEmpty()) {
      log.info("No accounts found to process for snapshot generation.");
      return balanceEntities;
    }

    accounts.forEach(account -> {
      Optional<BalanceEntity> previousSnapshotOpt = readBalanceRepository
          .findFirstByAccountRefIdAndProfileRefIdOrderByCreatedDateTimeDesc(account.getRefId(), account.getProfileId());

      if (previousSnapshotOpt.isPresent()) {
        processAccountWithPreviousSnapshot(account, previousSnapshotOpt.get(), periodEndTime, snapshotRunTime, balanceEntities);
      } else {
        processAccountWithoutPreviousSnapshot(account, periodEndTime, snapshotRunTime, balanceEntities);
      }
    });

    return balanceEntities;
  }

  private void processAccountWithPreviousSnapshot(AccountEntity account, BalanceEntity previousSnapshot,
      LocalDateTime effectiveToDateTime, LocalDateTime snapshotRunTime, List<BalanceEntity> balanceEntities) {

    LocalDateTime effectiveFromDateTime = previousSnapshot.getEffectiveToDateTime();
    String accountId = account.getRefId().toString();

    if (effectiveToDateTime.isBefore(effectiveFromDateTime) || effectiveToDateTime.isEqual(effectiveFromDateTime)) {
      log.info("Account: {} - Skipping snapshot, current one already exists for time {}",
          accountId, effectiveToDateTime);
      return;
    }

    log.info("Account: {} - === PROCESSING SNAPSHOT ===", accountId);
    log.info("Account: {} - Previous snapshot ID: {}", accountId, previousSnapshot.getId());
    log.info("Account: {} - Previous snapshot period: [{} to {}]",
        accountId,
        previousSnapshot.getEffectiveFromDateTime(),
        previousSnapshot.getEffectiveToDateTime());
    log.info("Account: {} - Previous balances - Total: {}, Pending: {}, Reserve: {}",
        accountId,
        previousSnapshot.getTotalAmount(),
        previousSnapshot.getTotalPendingAmount(),
        previousSnapshot.getTotalReserveAmount());
    log.info("Account: {} - New snapshot period: [{} to {}]",
        accountId, effectiveFromDateTime, effectiveToDateTime);

    TransactionAmounts amounts = fetchTransactionAmounts(account, effectiveFromDateTime, effectiveToDateTime, snapshotRunTime);

    // Calculate new balances
    BigDecimal newTotalAmount = previousSnapshot.getTotalAmount().add(amounts.getFinalizedAmountDelta());
    BigDecimal newTotalPendingAmount = amounts.getTotalPendingAmount();
    BigDecimal newTotalReserve = previousSnapshot.getTotalReserveAmount().add(amounts.getReserveTotalDelta());

    log.info("Account: {} - === BALANCE CALCULATION ===", accountId);
    log.info("Account: {} -   POSTED Balance Calculation:", accountId);
    log.info("Account: {} -     Previous Total:    {}", accountId, previousSnapshot.getTotalAmount());
    log.info("Account: {} -     + Finalized Delta: {}", accountId, amounts.getFinalizedAmountDelta());
    log.info("Account: {} -     = New Total:       {}", accountId, newTotalAmount);
    log.info("Account: {} - ", accountId);
    log.info("Account: {} -   PENDING Balance:", accountId);
    log.info("Account: {} -     Previous Pending:  {}", accountId, previousSnapshot.getTotalPendingAmount());
    log.info("Account: {} -     New Pending:       {}", accountId, newTotalPendingAmount);
    log.info("Account: {} -     Pending Change:    {}",
        accountId, newTotalPendingAmount.subtract(previousSnapshot.getTotalPendingAmount()));
    log.info("Account: {} - ", accountId);
    log.info("Account: {} -   RESERVE Balance:", accountId);
    log.info("Account: {} -     Previous Reserve:  {}", accountId, previousSnapshot.getTotalReserveAmount());
    log.info("Account: {} -     + Reserve Delta:   {}", accountId, amounts.getReserveTotalDelta());
    log.info("Account: {} -     = New Reserve:     {}", accountId, newTotalReserve);
    log.info("Account: {} - ", accountId);
    log.info("Account: {} -   FINAL BALANCES:", accountId);
    log.info("Account: {} -     Posted Balance:    {}", accountId, newTotalAmount);
    log.info("Account: {} -     Pending Balance:   {}", accountId, newTotalPendingAmount);
    log.info("Account: {} -     Reserve Balance:   {}", accountId, newTotalReserve);
    log.info("Account: {} -     Available Balance: {} (Posted + Pending)",
        accountId, newTotalAmount.add(newTotalPendingAmount));

    BalanceEntity newBalanceEntity = createBalanceEntity(account, effectiveFromDateTime, effectiveToDateTime,
        newTotalAmount, newTotalReserve, newTotalPendingAmount);

    // Log what we're about to save
    log.info("Account: {} -   SAVING new snapshot:", accountId);
    log.info("Account: {} -     Period: [{} to {}]",
        accountId, newBalanceEntity.getEffectiveFromDateTime(), newBalanceEntity.getEffectiveToDateTime());
    log.info("Account: {} -     Total Amount: {}", accountId, newBalanceEntity.getTotalAmount());
    log.info("Account: {} -     Pending Amount: {}", accountId, newBalanceEntity.getTotalPendingAmount());
    log.info("Account: {} -     Reserve Amount: {}", accountId, newBalanceEntity.getTotalReserveAmount());

    BalanceEntity savedEntity = balanceRepository.save(newBalanceEntity);
    balanceEntities.add(savedEntity);

    log.info("Account: {} - ✅ Successfully saved snapshot with ID: {}", accountId, savedEntity.getId());
    log.info("Account: {} - === END PROCESSING SNAPSHOT ===", accountId);
  }

  private void processAccountWithoutPreviousSnapshot(AccountEntity account,
      LocalDateTime effectiveToDateTime, LocalDateTime snapshotRunTime, List<BalanceEntity> balanceEntities) {

    // For the first snapshot, the start date is arbitrary but should be far in the past to capture all history.
    LocalDateTime effectiveFromDateTime = LocalDateTime.of(2000, 1, 1, 0, 0);
    logEffectiveDateTimes(account, effectiveFromDateTime, effectiveToDateTime);

    TransactionAmounts amounts = fetchTransactionAmounts(account, effectiveFromDateTime, effectiveToDateTime, snapshotRunTime);

    // For the first snapshot, the total amount *is* the delta.
    BigDecimal totalAmount = amounts.getFinalizedAmountDelta();
    BigDecimal totalPendingAmount = amounts.getTotalPendingAmount();
    BigDecimal totalReserve = amounts.getReserveTotalDelta();

    BalanceEntity newBalanceEntity = createBalanceEntity(account, effectiveFromDateTime, effectiveToDateTime,
        totalAmount, totalReserve, totalPendingAmount);

    balanceEntities.add(balanceRepository.save(newBalanceEntity));
    log.info("Generated FIRST balance snapshot for account {}: {}", account.getRefId(), newBalanceEntity);
  }

  private TransactionAmounts fetchTransactionAmounts(AccountEntity account, LocalDateTime periodStart,
      LocalDateTime periodEnd, LocalDateTime snapshotRunTime) {

    String accountId = account.getRefId().toString();
    String profileId = account.getProfileId().toString();

    log.info("Account: {} - fetchTransactionAmounts Profile: {} Period: [{} to {}] Snapshot run time: {}",
        accountId, profileId, periodStart, periodEnd, snapshotRunTime);

    // QUERY 1: Finalized Transactions Delta
    log.info("Account: {} - Querying FINALIZED transactions...", accountId);
    String finalizedQuery = String.format("""
        -- QUERY 1 (Finalized Delta)
        SELECT COALESCE(SUM(t.amount), 0)
        FROM transactions t
        WHERE t.account_ref_id = '%s'
          AND t.profile_ref_id = '%s'
          AND t.status IN ('POSTED', 'REVERSED')
          AND t.finalization_date_time >= '%s'
          AND t.finalization_date_time < '%s';
        """, accountId, profileId, periodStart, periodEnd);

    String finalizedQueryLog = finalizedQuery.replaceAll("\\s+", " ").trim();
    log.info("Account: {} - Executing Query: {}", accountId, finalizedQueryLog);

    Optional<BigDecimal> finalizedAmountDeltaOpt = readTransactionRepository.sumFinalizedAmountForSnapshotPeriod(
        account.getRefId(), account.getProfileId(), periodStart, periodEnd);
    BigDecimal finalizedAmountDelta = finalizedAmountDeltaOpt.orElse(BigDecimal.ZERO);
    log.info("Account: {} - Finalized Delta Result: {}", accountId, finalizedAmountDelta);

    // QUERY 2: Absolute pending amount at periodEnd
    log.info("Account: {} - Querying PENDING transactions as of period END...", accountId);

    // FIXED: Removed the created_date_time >= periodStart condition
    String pendingQuery = String.format("""
        -- QUERY 2 (Absolute Pending at periodEnd)
        SELECT COALESCE(SUM(t.amount), 0)
        FROM transactions t
        WHERE t.account_ref_id = '%s'
          AND t.profile_ref_id = '%s'
          AND (
            (t.status IN ('PENDING', 'INIT_PENDING')
             AND t.finalization_date_time IS NULL
             AND t.created_date_time <= '%s')
            OR
            (t.status IN ('POSTED', 'ROLLBACKED', 'ROLLBACKED_SYSTEM')
             AND t.finalization_date_time > '%s')
          );
        """, accountId, profileId, periodEnd, periodEnd);

    String pendingQueryLog = pendingQuery.replaceAll("\\s+", " ").trim();
    log.info("Account: {} - Executing Query: {}", accountId, pendingQueryLog);

    Optional<BigDecimal> totalPendingAmountOpt = readTransactionRepository.getTotalPendingAmountForAccountAsOf(
        account.getRefId(), account.getProfileId(), periodEnd);
    BigDecimal totalPendingAmount = totalPendingAmountOpt.orElse(BigDecimal.ZERO);
    log.info("Account: {} - Pending Amount Result: {}", accountId, totalPendingAmount);

    // QUERY 3: Reserve amount delta
    log.info("Account: {} - Querying RESERVE changes...", accountId);
    String reserveQuery = String.format(
        "getReserveAmountBS(accountRefId='%s', profileRefId='%s', periodStart='%s', periodEnd='%s')",
        accountId, profileId, periodStart, periodEnd);
    log.info("Account: {} - QUERY 3 (Reserve Delta): {}", accountId, reserveQuery);

    Optional<BigDecimal> reserveTotalDeltaOpt = readTransactionRepository.getReserveAmountBS(
        account.getRefId(), account.getProfileId(), periodStart, periodEnd);
    BigDecimal reserveTotalDelta = reserveTotalDeltaOpt.orElse(BigDecimal.ZERO);
    log.info("Account: {} - Reserve Delta Result: {}", accountId, reserveTotalDelta);

    log.info("Account: {} - === TRANSACTION AMOUNTS SUMMARY ===", accountId);
    log.info("Account: {} -   Finalized Delta:  {}", accountId, finalizedAmountDelta);
    log.info("Account: {} -   Pending Amount:   {} (absolute at periodEnd: {})", accountId, totalPendingAmount, periodEnd);
    log.info("Account: {} -   Reserve Delta:    {}", accountId, reserveTotalDelta);

    TransactionAmounts result = TransactionAmounts.builder()
        .finalizedAmountDelta(finalizedAmountDelta)
        .totalPendingAmount(totalPendingAmount)
        .reserveTotalDelta(reserveTotalDelta)
        .build();

    return result;
  }

  private BalanceEntity createBalanceEntity(AccountEntity account, LocalDateTime from, LocalDateTime to,
      BigDecimal totalAmount, BigDecimal totalReserve, BigDecimal pendingTotal) {

    BalanceEntity balanceEntity = new BalanceEntity();
    balanceEntity.setMonetaryUnit(MonetaryUnit.valueOf(String.valueOf(account.getMonetaryUnit())));
    balanceEntity.setProfileRefId(account.getProfileId());
    balanceEntity.setAccountRefId(account.getRefId());
    balanceEntity.setEffectiveFromDateTime(from);
    balanceEntity.setEffectiveToDateTime(to);
    balanceEntity.setTotalAmount(totalAmount);
    balanceEntity.setTotalPendingAmount(pendingTotal);
    balanceEntity.setTotalReserveAmount(totalReserve);
    balanceEntity.setTotalAmountCredit(BigDecimal.ZERO);
    balanceEntity.setTotalAmountDebit(BigDecimal.ZERO);

    // Add snapshot calculation time for debugging
    BigDecimal availableBalance = totalAmount.add(pendingTotal);
    log.debug("Balance Entity created - Total: {}, Pending: {}, Available: {}",
        totalAmount, pendingTotal, availableBalance);

    return balanceEntity;
  }

  private void logEffectiveDateTimes(AccountEntity account, LocalDateTime from, LocalDateTime to) {
    log.info("Processing snapshot for account {} for period [{} to {}]", account.getRefId(), from, to);
  }

  @Data
  @Builder
  private static class TransactionAmounts {

    private BigDecimal finalizedAmountDelta;
    private BigDecimal totalPendingAmount;
    private BigDecimal reserveTotalDelta;
  }
}