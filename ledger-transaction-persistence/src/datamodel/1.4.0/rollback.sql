-- This script reverts the changes made in alter.sql for version 1.4.0
ALTER TABLE balance
    DROP COLUMN IF EXISTS total_pending_amount;

-- Step 1: Drop the new function and re-create the old version
DROP FUNCTION IF EXISTS sumAmountByDateRangeInstructionId(UUI<PERSON>, UUID, <PERSON><PERSON>ESTA<PERSON>, <PERSON><PERSON>ESTA<PERSON>, INTEGER);
CREATE OR REPLACE FUNCTION sumAmountByDateRangeInstructionId(
    p_accountRefId UUID,
    p_profileRefId UUID,
    p_effectiveDateTimeStart TIMESTAMP,
    p_effectiveDateTimeUntil TIMESTAMP,
    p_instructionId INTEGER
) RETURNS NUMERIC AS $$
DECLARE
    v_sum NUMERIC;
BEGIN
    SELECT sum(amount) INTO v_sum
    FROM transactions
    WHERE
        account_ref_id = p_accountRefId AND
        profile_ref_id = p_profileRefId AND
        status IN ('POSTED','PENDING','INIT_PENDING','REVERSED') AND
        (effective_date_time BETWEEN p_effectiveDateTimeStart AND p_effectiveDateTimeUntil) AND
        instruction_id <= p_instructionId;
    RETURN v_sum;
END; $$ LANGUAGE plpgsql parallel safe;

-- Step 2: Drop the indexes that were created in alter.sql
DROP INDEX IF EXISTS idx_transactions_balance_check_v2;
DROP INDEX IF EXISTS idx_trans_realtime_balance_posted;
DROP INDEX IF EXISTS idx_trans_realtime_balance_reversed;
