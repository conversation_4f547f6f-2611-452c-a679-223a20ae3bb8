ALTER TABLE balance
    ADD COLUMN IF NOT EXISTS total_pending_amount NUMERIC(13, 2) NULL;

-- Step 1: Drop the old function if it exists
DROP FUNCTION IF EXISTS sumAmountByDateRangeInstructionId(U<PERSON><PERSON>, U<PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, INTEGER);

-- Step 2: Create the function with the 3 separate queries
CREATE OR REPLACE FUNCTION sumAmountByDateRangeInstructionId(
    p_account_ref_id UUID,
    p_profile_ref_id UUID,
    p_snapshot_time TIMESTAMP,
    p_current_time TIMESTAMP,
    p_instruction_id INT
)
    RETURNS NUMERIC AS $$
DECLARE
    new_since_snapshot NUMERIC;
    reversed_since_snapshot NUMERIC;
    pending_to_posted NUMERIC;
    total_to_be_given_back_to_account NUMERIC;
    total_change NUMERIC;
BEGIN
    -- Query 1: Combined NEW and REVERSED transactions since snapshot (Proven Fast)
    SELECT
        COALESCE(SUM(CASE WHEN status IN ('PENDING', 'INIT_PENDING', 'POSTED') THEN amount ELSE 0 END), 0),
        COALESCE(SUM(CASE WHEN status = 'REVERSED' AND related_id IS NOT NULL THEN amount ELSE 0 END), 0)
    INTO new_since_snapshot, reversed_since_snapshot
    FROM transactions
    WHERE account_ref_id = p_account_ref_id
      AND profile_ref_id = p_profile_ref_id
      AND created_date_time > p_snapshot_time
      AND created_date_time <= p_current_time
      AND instruction_id < p_instruction_id;

    -- Query 2: TRANSITION: PENDING -> POSTED (Original Fast Query)
    SELECT COALESCE(SUM(amount), 0)
    INTO pending_to_posted
    FROM transactions
    WHERE account_ref_id = p_account_ref_id
      AND profile_ref_id = p_profile_ref_id
      AND created_date_time <= p_snapshot_time
      AND created_date_time > p_snapshot_time - INTERVAL '33 days'
      AND status = 'POSTED'
      AND finalization_date_time > p_snapshot_time
      AND finalization_date_time <= p_current_time
      AND instruction_id < p_instruction_id;

    -- Query 3: TRANSITION: PENDING -> ROLLBACKED/ROLLBACKED_SYSTEM and POSTED -> REVERSED (Original Fast Query)
    SELECT COALESCE(SUM(amount), 0)
    INTO total_to_be_given_back_to_account
    FROM transactions
    WHERE account_ref_id = p_account_ref_id
      AND profile_ref_id = p_profile_ref_id
      AND created_date_time <= p_snapshot_time
      AND created_date_time > p_snapshot_time - INTERVAL '33 days'
      AND related_id IS NULL
      AND status IN ('REVERSED', 'ROLLBACKED', 'ROLLBACKED_SYSTEM')
      AND finalization_date_time > p_snapshot_time
      AND finalization_date_time <= p_current_time
      AND instruction_id < p_instruction_id;

    -- FINAL CALCULATION:
    total_change := new_since_snapshot
                    + reversed_since_snapshot
                    + pending_to_posted
                    - total_to_be_given_back_to_account;

    RETURN total_change;
END;
$$ LANGUAGE plpgsql PARALLEL SAFE;

-- Step 3: Create the optimized indexes if they don't already exist
-- This index is critical for the UNION ALL query's performance
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_transactions_balance_check_v2
    ON transactions (account_ref_id, profile_ref_id, created_date_time, instruction_id)
    INCLUDE (amount, status, related_id);

-- An improved index for the 'pending_to_posted' transition
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_trans_realtime_balance_posted
    ON transactions (account_ref_id, profile_ref_id, status, finalization_date_time, created_date_time)
    INCLUDE (amount, instruction_id)
    WHERE status = 'POSTED' AND instruction_id IS NOT NULL;

-- An improved index for reversed and rollbacked transactions
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_trans_realtime_balance_reversed
    ON transactions (account_ref_id, profile_ref_id, status, finalization_date_time, created_date_time)
    INCLUDE (amount, instruction_id, related_id)
    WHERE status IN ('REVERSED', 'ROLLBACKED', 'ROLLBACKED_SYSTEM')
      AND related_id IS NULL
      AND instruction_id IS NOT NULL;

-- Step 4: Update statistics after creating indexes
ANALYZE transactions;