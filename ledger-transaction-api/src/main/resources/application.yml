spring:
  main:
    allow-bean-definition-overriding: true
    disable-cognito-jwt-verification: ${DISABLE_COGNITO_JWT_VERIFICATION}
  datasource:
    transaction-rw:
      url: **************************************************************************
      username: postgres
      password: postgres
      platform: postgres
      poolName: transaction-rw
      maximumPoolSize: 50
      connectionTimeout: 1000
      minimumIdle: 10
      idleTimeout: 30000 # 30 seconds
      maxLifetime: 1800000 # 30 minutes

    transaction-ro:
      url: **************************************************************************
      username: postgres
      password: postgres
      platform: postgres
      poolName: transaction-ro
      maximumPoolSize: 20
      connectionTimeout: 1000
      minimumIdle: 10
      idleTimeout: 30000 # 30 seconds
      maxLifetime: 1800000 # 30 minutes

  jackson:
    default-property-inclusion: NON_NULL
    deserialization:
      FAIL_ON_UNKNOWN_PROPERTIES: true
  jpa:
#    show-sql: true
    properties:
      hibernate:
#        format_sql: true
#        use_sql_comments: true
        jdbc.lob.non_contextual_creation: true
        dialect: org.hibernate.dialect.PostgreSQLDialect
        jdbc:
          batch_size: 100
        order_inserts: true
        order_updates: true
        generate_statistics: false
logging:
  level:
    com:
      zaxxer:
        hikari: INFO
      peoplestrust: ${LOG_LEVEL_COM_PEOPLESTRUST}
  root: WARN  # Change this to ERROR for even fewer logs
  org.hibernate.SQL: ${LOG_LEVEL_HIBERNATE}
  APIPayloadLogger: ${LOG_LEVEL_API_PAYLOAD_LOGGER}
  PerfLogger: ${LOG_LEVEL_PERF_LOGGER}
  FlowLogger: ${LOG_LEVEL_FLOW_LOGGER}

server:
  tomcat:
    mbeanregistry:
      enabled: true
management:
  endpoints:
    web:
      exposure:
        include: info,health,metrics
app:
  name: TransactionAPI
  usespforsum: false
transaction:
  api:
    validation:
      timeout:
        connection: 2000
        read: 2000
    timetolive: 30000
    account:
      url: http://localhost:8092/v1/internal/account
      validate:
        url: http://localhost:8092/v1/internal/account/validate
    profile:
      url: http://localhost:8091/v1/internal/profile