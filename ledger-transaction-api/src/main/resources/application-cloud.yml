spring:
  datasource:
    transaction-rw:
      url: jdbc:postgresql://${READ_WRITE_DB_HOST}:${READ_WRITE_DB_PORT}/${READ_WRITE_DB_NAME}?currentSchema=${TRANSACTION_DB_SCHEMA}
      username: ${READ_WRITE_DB_USERNAME}
      password: ${READ_WRITE_DB_PASSWORD}
      maximumPoolSize: ${MAXIMUM_POOL_SIZE}
      connectionTimeout: ${DB_CONNECTION_TIME_OUT}
    transaction-ro:
      url: jdbc:postgresql://${READ_DB_HOST}:${READ_DB_PORT}/${READ_DB_NAME}?currentSchema=${TRANSACTION_DB_SCHEMA}
      username: ${READ_DB_USERNAME}
      password: ${READ_DB_PASSWORD}
      maximumPoolSize: ${MAXIMUM_POOL_SIZE}
      connectionTimeout: ${DB_CONNECTION_TIME_OUT}
  main:
    allow-bean-definition-overriding: true
  disable-cognito-jwt-verification: ${DISABLE_COGNITO_JWT_VERIFICATION}
  jackson:
    default-property-inclusion: non_empty
    deserialization:
      FAIL_ON_UNKNOWN_PROPERTIES: true
  jpa:
    properties:
      hibernate:
        jdbc.lob.non_contextual_creation: true
        dialect: org.hibernate.dialect.PostgreSQLDialect
        jdbc:
          batch_size: 100
        order_inserts: true
        order_updates: true
        generate_statistics: false
server:
  tomcat:
    mbeanregistry:
      enabled: true
management:
  endpoints:
    web:
      exposure:
        include: info,health,metrics
app:
  name: TransactionAPI
  usespforsum: ${USE_STORE_PROCEDURE_FOR_SUM}

transaction:
  api:
    validation:
      timeout:
        connection: ${TRANSACTION_VALIDATION_SERVICE_CONNECTION_TIMEOUT}
        read: ${TRANSACTION_VALIDATION_SERVICE_READ_TIMEOUT}
    timetolive: 30000
    profile:
      url: ${PROFILE_API_URL}
    account:
      url: ${ACCOUNT_API_URL}
      validate:
        url: ${PROFILE_ACCOUNT_VALIDATE_API_URL}
logging:
  level:
    com:
      zaxxer:
        hikari: INFO
      peoplestrust: ${LOG_LEVEL_COM_PEOPLESTRUST}
    root: WARN  # Change this to ERROR for even fewer logs
    org.hibernate.SQL: ${LOG_LEVEL_HIBERNATE}
    APIPayloadLogger: ${LOG_LEVEL_API_PAYLOAD_LOGGER}
    PerfLogger: ${LOG_LEVEL_PERF_LOGGER}
    FlowLogger: ${LOG_LEVEL_FLOW_LOGGER}

