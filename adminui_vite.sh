#!/usr/bin/env bash
set -euo pipefail

##############################################################################
# Deploy Vite front‑end to S3 + CloudFront
#
# Usage: ./adminui_vite.sh [qa|staging] [aws_profile]
#  – First arg (required):  Environment  (qa | staging)
#  – Second arg (optional): AWS profile  (default: NON_LEDGER)
##############################################################################

ENVIRONMENT=${1:-}
AWS_PROFILE=${2:-NON_LEDGER}

# Normalise env to lowercase so "QA" / "Qa" also work
ENVIRONMENT=$(echo "$ENVIRONMENT" | tr '[:upper:]' '[:lower:]')

# Pick bucket & distribution
case "$ENVIRONMENT" in
  qa)
    BUCKET="gl-qa-799455639446"
    DISTRO_ID="E16KRLV292Y0P0"
    ;;
  staging)
    BUCKET="gl-staging-799455639446"
    DISTRO_ID="ETN21A52AJ36F"
    ;;
  *)
    echo "❌  Invalid environment '$ENVIRONMENT'. Use 'qa' or 'staging'."
    exit 1
    ;;
esac

echo "🔧  AWS profile : $AWS_PROFILE"
echo "🚀  Environment : $ENVIRONMENT"
echo "🪣  S3 bucket   : $BUCKET"
echo "🌐  CloudFront  : $DISTRO_ID"
echo "──────────────────────────────────────────────────────────────"

# ───────────────────────────────────────────────────────────────
# 1. Go to the project root /frontend
# ───────────────────────────────────────────────────────────────
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
cd "$SCRIPT_DIR/frontend" || { echo "❌  'frontend' dir not found."; exit 1; }

# ───────────────────────────────────────────────────────────────
# 2. Install dependencies (fallback to npm install if npm ci fails)
# ───────────────────────────────────────────────────────────────
echo "📦  Installing dependencies…"
if [[ -f package-lock.json ]]; then
  if ! npm ci --legacy-peer-deps --progress=true --loglevel=info; then
    echo "⚠️  npm ci failed (likely old or missing lock‑file format) — retrying with npm install"
    npm install --legacy-peer-deps --progress=true --loglevel=info
  fi
else
  npm install --legacy-peer-deps --progress=true --loglevel=info
fi
echo "✅  Dependencies installed successfully!"

# ───────────────────────────────────────────────────────────────
# 3. Build with the correct env file  (vite ➜ dist/)
# ───────────────────────────────────────────────────────────────
echo "🛠️   Building with Vite for '$ENVIRONMENT'…"
npm run build:"$ENVIRONMENT"

# ───────────────────────────────────────────────────────────────
# 4. Upload to S3  (—delete removes orphaned files in the bucket)
# ───────────────────────────────────────────────────────────────
echo "☁️   Syncing dist/ → s3://$BUCKET …"
aws s3 sync dist/ "s3://$BUCKET" \
  --delete \
  --profile "$AWS_PROFILE"

# ───────────────────────────────────────────────────────────────
# 5. Invalidate CloudFront so users get the fresh build
# ───────────────────────────────────────────────────────────────
echo "🚚  Creating CloudFront invalidation …"
aws cloudfront create-invalidation \
  --distribution-id "$DISTRO_ID" \
  --paths '/*' \
  --profile "$AWS_PROFILE" >/dev/null

echo "✅  Deployment finished for '$ENVIRONMENT'."
