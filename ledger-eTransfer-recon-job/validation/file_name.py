import os
import re
import logging
from urllib.parse import urlparse
from typing import Optional, Dict, Any
from .datadog import trigger_datadog_alert
from utils.database import db_connection

logger = logging.getLogger("eTransfer_recon.validation.file_name")

def extract_account_id_from_filename(file_name: str) -> Optional[str]:
    # Pattern to match: etransfer_gl_recon_{account_id}_{YYYYMMDD}.xml
    pattern = r"etransfer_gl_recon_([a-f0-9\-]{36})_\d{8}\.xml"
    match = re.match(pattern, file_name)

    if match:
        return match.group(1)
    return None

def validate_account_in_database(account_ref_id: str, file_name: str = None) -> Dict[str, Any]:
    try:
        validation_result = db_connection.validate_account_ref_id(account_ref_id)

        if validation_result['valid']:
            logger.info(f"Account validation successful: {account_ref_id} - {validation_result['account_name']} ({validation_result['status']})")
        else:
            logger.error(f"Account validation failed: {account_ref_id} - {validation_result.get('error', 'Unknown error')}")
            trigger_datadog_alert(f"Account validation failed: {account_ref_id}")

        return validation_result

    except Exception as e:
        error_msg = f"Database validation error for account {account_ref_id}: {e}"
        logger.error(error_msg)
        trigger_datadog_alert(error_msg)

        return {
            'valid': False,
            'account_ref_id': account_ref_id,
            'error': error_msg
        }

def validate_file_name(file_path: str, account_id: Optional[str] = None) -> bool:
    # Validate file path
    if not file_path.startswith("s3://") and not os.path.isfile(file_path):
        logger.error(f"Invalid file_path: '{file_path}'. Expected an S3 path or local file.")
        trigger_datadog_alert(f"Invalid file_path: {file_path}")
        return False

    # Extract filename
    if file_path.startswith("s3://"):
        parsed_url = urlparse(file_path)
        file_name = os.path.basename(parsed_url.path)
    else:
        file_name = os.path.basename(file_path)

    # Extract account ID from filename
    extracted_account_id = extract_account_id_from_filename(file_name)

    if account_id is None:
        if extracted_account_id:
            account_id = extracted_account_id
            logger.info(f"Extracted account ID from filename: {account_id}")
        else:
            logger.error(f"No account ID found in filename: '{file_name}'. Expected format: 'etransfer_gl_recon_$AccountID_YYYYMMDD.xml'")
            trigger_datadog_alert(f"No account ID found in filename: {file_name}")
            return False

    if not account_id:
        logger.error("No account ID available for validation.")
        trigger_datadog_alert("No account ID available for validation")
        return False

    # Validate filename format
    pattern = rf"etransfer_gl_recon_{re.escape(account_id)}_[0-9]{{8}}\.xml"
    if not re.fullmatch(pattern, file_name):
        logger.error(f"Invalid file name: '{file_name}' and account: '{account_id}'. Expected format: 'etransfer_gl_recon_$AccountID_YYYYMMDD.xml'")
        trigger_datadog_alert(f"Invalid file name: {file_name}")
        return False

    # Validate account ID against database
    logger.info(f"Validating account ID '{account_id}' against database...")
    db_validation = validate_account_in_database(account_id, file_name)

    if not db_validation['valid']:
        logger.error(f"Account validation failed: {db_validation.get('error', 'Unknown error')}")
        trigger_datadog_alert(f"Account validation failed: {account_id}")
        return False

    # Check if account is active
    if db_validation.get('status') != 'ACTIVE':
        logger.warning(f"Account {account_id} is not active (status: {db_validation.get('status')})")
        trigger_datadog_alert(f"Account {account_id} is not active")
        # Note: We don't return False here as the account exists, just not active

    logger.info(f"File name: '{file_name}' and account ID: '{account_id}' validated successfully.")
    return True