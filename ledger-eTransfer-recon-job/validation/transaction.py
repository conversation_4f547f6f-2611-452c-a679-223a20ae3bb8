from lxml import etree
from dateutil.parser import isoparse
import logging
from .datadog import trigger_datadog_alert
from utils.metrics import init_datadog, increment, gauge, timing, event

logger = logging.getLogger("eTransfer_recon.validation.transaction")

def validate_transaction_fields(doc: etree._ElementTree, file_name: str = None) -> dict:
    try:
        root = doc.getroot()
        ns = {'ns': root.nsmap[None]}

        stmt = root.find('.//ns:BkToCstmrStmt/ns:Stmt', namespaces=ns)
        if stmt is None:
            logger.error("Missing <Stmt> element for transaction validation")
            trigger_datadog_alert("Missing <Stmt> element for transaction validation")
            return {
                'valid': False,
                'total_transactions': 0,
                'valid_transactions': 0,
                'invalid_transactions': [],
                'errors': ["Missing <Stmt> element for transaction validation"]
            }

        entries = stmt.findall('ns:Ntry', namespaces=ns)
        total_transactions = len(entries)
        valid_transactions = 0
        invalid_transactions = []
        errors = []

        logger.info(f"Starting validation of {total_transactions} transactions")

        # Updated list of valid statuses
        valid_statuses = [
            "ACCEPTED", "QUEUED", "AVAILABLE", "DEPOSIT_INITIATED",
            "DEPOSIT_PENDING", "FAILED_PENDING", "DEPOSIT_COMPLETE",
            "COMPLETE", "CANCELLED", "DECLINED", "EXPIRED",
            "SECURITY_ANSWER_FAILURE", "FAILED", "DEPOSIT_FAILED"
        ]

        for i, ntry in enumerate(entries, 1):
            transaction_errors = []
            transaction_valid = True

            amt_el = ntry.find('ns:Amt', namespaces=ns)
            ccy = amt_el.get('Ccy') if amt_el is not None else None
            amt_val = float(amt_el.text) if amt_el is not None and amt_el.text else None

            cdt_dbt_el = ntry.find('ns:CdtDbtInd', namespaces=ns)
            cdt_dbt_val = cdt_dbt_el.text.strip() if cdt_dbt_el is not None and cdt_dbt_el.text else None

            status_el = ntry.find('ns:Sts', namespaces=ns)
            status = status_el.text.strip() if status_el is not None and status_el.text else None

            bookg_dt_el = ntry.find('ns:BookgDtTm', namespaces=ns)
            bookg_dt = bookg_dt_el.text.strip() if bookg_dt_el is not None and bookg_dt_el.text else None

            ntry_ref_el = ntry.find('ns:NtryRef', namespaces=ns)
            ntry_ref = ntry_ref_el.text.strip() if ntry_ref_el is not None and ntry_ref_el.text else None

            tx_el = ntry.find('.//ns:NtryDtls/ns:TxDtls', namespaces=ns)

            if tx_el is None:
                error_msg = f"Transaction {i}: Missing <TxDtls>."
                logger.error(error_msg)
                trigger_datadog_alert(error_msg)
                transaction_errors.append(error_msg)
                transaction_valid = False
                refs_el = None
            else:
                refs_el = tx_el.find('ns:Refs', namespaces=ns)

            end_to_end_id_el = refs_el.find('ns:EndToEndId', namespaces=ns) if refs_el is not None else None
            end_to_end_id = end_to_end_id_el.text.strip() if end_to_end_id_el is not None and end_to_end_id_el.text else None
            logger.info(f"Transaction {i}: end_to_end_id: {end_to_end_id}")

            initn_tmstmp_el = refs_el.find('ns:InitnTmstmp', namespaces=ns) if refs_el is not None else None
            initn_tmstmp = initn_tmstmp_el.text.strip() if initn_tmstmp_el is not None and initn_tmstmp_el.text else None
            logger.info(f"Transaction {i}: initn_tmstmp: {initn_tmstmp}")

            compltn_tmstmp_el = refs_el.find('ns:CompltnTmstmp', namespaces=ns) if refs_el is not None else None
            compltn_tmstmp = compltn_tmstmp_el.text.strip() if compltn_tmstmp_el is not None and compltn_tmstmp_el.text else None
            logger.info(f"Transaction {i}: compltn_tmstmp: {compltn_tmstmp}")

            # Validate individual fields and collect errors
            if not ntry_ref:
                error_msg = f"Transaction {i}: Missing EntryRef."
                logger.error(error_msg)
                trigger_datadog_alert(error_msg)
                transaction_errors.append(error_msg)
                transaction_valid = False

            if amt_val is None or amt_val < 0:
                error_msg = f"Transaction {i}: Invalid or missing amount: {amt_val}."
                logger.error(error_msg)
                trigger_datadog_alert(error_msg)
                transaction_errors.append(error_msg)
                transaction_valid = False

            if not ccy or ccy != 'CAD':
                error_msg = f"Transaction {i}: Invalid or missing currency (must be CAD): {ccy}."
                logger.error(error_msg)
                trigger_datadog_alert(error_msg)
                transaction_errors.append(error_msg)
                transaction_valid = False

            if cdt_dbt_val not in ('CRDT', 'DBIT'):
                error_msg = f"Transaction {i}: Invalid credit/debit indicator: {cdt_dbt_val}."
                logger.error(error_msg)
                trigger_datadog_alert(error_msg)
                transaction_errors.append(error_msg)
                transaction_valid = False

            # Validate the status against the updated list of valid statuses
            if status not in valid_statuses:
                error_msg = f"Transaction {i}: Invalid status: {status}. Must be one of {valid_statuses}."
                logger.error(error_msg)
                trigger_datadog_alert(error_msg)
                transaction_errors.append(error_msg)
                transaction_valid = False

            if not end_to_end_id:
                error_msg = f"Transaction {i}: Missing EndToEndId."
                logger.error(error_msg)
                trigger_datadog_alert(error_msg)
                transaction_errors.append(error_msg)
                transaction_valid = False

            # Validate timestamps
            timestamp_validations = [
                ("Entry Timestamp", bookg_dt),
                ("Initiated Timestamp", initn_tmstmp),
                ("Completed Timestamp", compltn_tmstmp)
            ]

            for ts_name, ts_val in timestamp_validations:
                if not ts_val:
                    error_msg = f"Transaction {i}: Missing {ts_name}."
                    logger.error(error_msg)
                    trigger_datadog_alert(error_msg)
                    transaction_errors.append(error_msg)
                    transaction_valid = False
                else:
                    try:
                        parsed_ts = isoparse(ts_val)
                        _ = parsed_ts.year, parsed_ts.month, parsed_ts.day, parsed_ts.hour, parsed_ts.minute, parsed_ts.second
                    except Exception:
                        error_msg = f"Transaction {i}: Invalid {ts_name}: {ts_val}."
                        logger.error(error_msg)
                        trigger_datadog_alert(error_msg)
                        transaction_errors.append(error_msg)
                        transaction_valid = False

            # Record transaction results
            if transaction_valid:
                valid_transactions += 1
                logger.info(f"Transaction {i}: Validation successful")
            else:
                invalid_transaction_info = {
                    'transaction_index': i,
                    'entry_ref': ntry_ref,
                    'end_to_end_id': end_to_end_id,
                    'amount': amt_val,
                    'currency': ccy,
                    'credit_debit_indicator': cdt_dbt_val,
                    'status': status,
                    'errors': transaction_errors
                }
                invalid_transactions.append(invalid_transaction_info)
                errors.extend(transaction_errors)

                # Log detailed error information for invalid transaction
                logger.error(f"Transaction {i} validation failed:")
                for error in transaction_errors:
                    logger.error(f"  - {error}")

        overall_valid = len(invalid_transactions) == 0
        logger.info(f"Transaction validation summary: {valid_transactions}/{total_transactions} valid, {len(invalid_transactions)} invalid")

        base_tags = [f"filename:{file_name}"] if file_name else []

        gauge("recon.transaction.total_count", total_transactions, tags=base_tags)
        gauge("recon.transaction.valid_count", valid_transactions, tags=base_tags)
        gauge("recon.transaction.invalid_count", len(invalid_transactions), tags=base_tags)

        if total_transactions > 0:
            success_rate = (valid_transactions / total_transactions) * 100
            gauge("recon.transaction.success_rate", success_rate, tags=base_tags)

            from utils.metrics import histogram
            histogram("recon.transaction.batch_size", total_transactions, tags=base_tags)

        if invalid_transactions:
            logger.warning(f"Found {len(invalid_transactions)} invalid transactions. Processing will continue for valid transactions.")
            trigger_datadog_alert(f"Found {len(invalid_transactions)} invalid transactions in eTransfer reconciliation file")

        return {
            'valid': overall_valid,
            'total_transactions': total_transactions,
            'valid_transactions': valid_transactions,
            'invalid_transactions': invalid_transactions,
            'errors': errors
        }

    except Exception as e:
        error_msg = f"Transaction validation error: {e}"
        logger.error(error_msg)
        trigger_datadog_alert(error_msg)
        return {
            'valid': False,
            'total_transactions': 0,
            'valid_transactions': 0,
            'invalid_transactions': [],
            'errors': [error_msg]
        }
