## eTransfer Recon Job

This job processes eTransfer reconciliation XML files from S3 buckets. It can be triggered automatically via S3 events through a Lambda function or run manually.

### Deployment Modes

#### 1. **S3 Event-Driven (Production Mode)**
- Files dropped in S3 bucket automatically trigger Lambda function
- <PERSON><PERSON> creates Kubernetes Job with dynamic S3 bucket and key parameters
- No hardcoded file names - processes the actual file that triggered the event

#### 2. **Manual Execution**
- Can be run locally for development/testing
- Can be triggered manually in Kubernetes

### Build and Push Docker Image

```bash
cd ledger-eTransfer-recon-job
sh deploy.sh
```

### Local Development & Testing

#### 1. Install dependencies
```bash
pip install -r requirements.txt
```

#### 2. Run the job locally
```bash
# For S3 files
python main.py --s3-bucket <bucket-name> --s3-key <file-key>

# For local files
python main.py --local-file <path-to-xml-file>
```

#### 3. Run unit tests
```bash
pytest tests/
```

**Environment Setup:**
- Set the environment variable `GL_ACCOUNT_ID` before running the job for file name validation
- Configure AWS credentials for S3 access (ACCESS_KEY_ID, SECRET_ACCESS_KEY, AWS_REGION)

**Validation Features:**
- File name validation against account ID
- XML schema validation using XSD
- Header field validation
- Transaction business rule validation

### Kubernetes Operations

#### 1. Manual job execution (without Lambda)
```bash
kubectl create job --from=cronjob/etransfer-recon-cronjob manual-test-job -n pg-ledger-qa
```

#### 2. Check job status and logs
```bash
# Get job pods
kubectl get pods -n pg-ledger-qa -l job-name=<job-name>

# View job logs
kubectl logs -n pg-ledger-qa $(kubectl get pods -n pg-ledger-qa -l job-name=<job-name> -o name)
```

#### 3. Build and push Docker image manually
```bash
cd ledger-eTransfer-recon-job
docker build --platform linux/amd64 -t etransfer-recon-job .
docker image ls | awk '$1 == "etransfer-recon-job" && $2 == "latest" {print $3}'
docker tag <image-id> ************.dkr.ecr.ca-central-1.amazonaws.com/etransfer-recon-job:<tag>
docker push ************.dkr.ecr.ca-central-1.amazonaws.com/etransfer-recon-job:<tag>
```

### S3 Event Integration

The job is designed to work with the Lambda function in `eks-lambda/` that:
- Listens for S3 ObjectCreated events
- Extracts bucket and key information from the event
- Creates Kubernetes Jobs with dynamic S3 parameters
- Processes the exact file that triggered the event

**Environment-Specific S3 Buckets:**
- QA: `pg-gl-recon-qa`
- Staging: `pg-gl-recon-staging`
- Production: `pg-gl-recon-prod`