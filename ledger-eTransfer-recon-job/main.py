import argparse
import logging
import os
import shutil
from validation.file_name import validate_file_name
from validation.header import parse_and_validate_headers
from validation.transaction import validate_transaction_fields
from utils.s3 import download_s3_file
from lxml import etree
from dotenv import load_dotenv

# Custom metrics wrapper
from utils.metrics import init_datadog, increment, gauge, timing, event, track_file_processing_metrics, track_validation_error_details

logger = logging.getLogger("eTransfer_recon.main")
logging.basicConfig(level=logging.INFO)

XSD_PATH = os.path.join(os.path.dirname(__file__), 'specification/camt053_etransfer_recon.xsd')

# Load env vars
load_dotenv()

def parse_and_validate_xml(xml_path: str, xsd_path: str) -> etree._ElementTree:
    try:
        with open(xsd_path, 'rb') as schema_file:
            schema_doc = etree.parse(schema_file)
            schema = etree.XMLSchema(schema_doc)

        parser = etree.XMLParser(schema=schema)
        with open(xml_path, 'rb') as xml_file:
            doc = etree.parse(xml_file, parser)

        logger.info("XML validated successfully against the XSD")
        return doc
    except etree.XMLSchemaError as e:
        logger.error("XSD validation error: {e}")
        raise
    except etree.XMLSyntaxError as e:
        logger.error("XML syntax error: {e}")
        raise

def etransfer_recon(file_path: str) -> dict:
    logger.info(f"Running eTransfer recon for file: {file_path}")

    result = {
        'success': False,
        'file_name': os.path.basename(file_path),
        'validation_summary': None,
        'errors': []
    }

    if not validate_file_name(file_path):
        result['errors'].append("File name validation failed")
        return result

    try:
        doc = parse_and_validate_xml(file_path, XSD_PATH)
    except Exception as e:
        error_msg = f"Failed to parse XML: {e}"
        logger.error(error_msg)
        result['errors'].append(error_msg)
        return result

    if not parse_and_validate_headers(doc):
        result['errors'].append("Header validation failed")
        return result

    # Validate transactions and get detailed results with filename
    file_name = os.path.basename(file_path)
    transaction_results = validate_transaction_fields(doc, file_name)
    result['validation_summary'] = transaction_results

    # Business-focused metrics: file processing status and transaction counts
    file_tags = [f"filename:{result['file_name']}"]

    if transaction_results['valid']:
        logger.info("eTransfer Recon Completed Successfully.")
        result['success'] = True
        increment("s3.file_processed.success", tags=file_tags)
    else:
        logger.warning(f"eTransfer Recon completed with validation issues: {transaction_results['valid_transactions']}/{transaction_results['total_transactions']} transactions valid")
        result['success'] = True
        increment("s3.file_processed.success_with_validation_issues", tags=file_tags)

    # Track business metrics
    track_file_processing_metrics(result['file_name'], result)

    #Track detailed validation error metrics
    if transaction_results['invalid_transactions']:
        track_validation_error_details(result['file_name'], transaction_results['invalid_transactions'])

    return result


def main():
    logger.info("Starting main recon job entrypoint.")

    parser = argparse.ArgumentParser(description="Run eTransfer recon job (single file per invocation)")
    logger.info("Argument parser initialized with job description.")

    group = parser.add_mutually_exclusive_group(required=True)
    logger.info("Added mutually exclusive argument group for S3 vs local file.")

    group.add_argument("--s3-bucket", help="S3 bucket name (production mode)")
    logger.info("🪣 Added argument --s3-bucket to parser.")

    parser.add_argument("--s3-key", help="S3 object key (production mode)")
    logger.info("Added argument s3-key to parser.")

    group.add_argument("--local-file", help="Local XML file path (dev/test mode)")
    logger.info("Added argument local-file to parser.")

    args = parser.parse_args()
    logger.info("Parsed command-line arguments:args")



    file_path = None
    if args.s3_bucket and args.s3_key:
        file_path = download_s3_file(args.s3_bucket, args.s3_key)
    elif args.local_file:
        file_path = args.local_file
    else:
        logger.error("You must provide either --s3-bucket and --s3-key, or --local-file.")
        return

    try:
        recon_result = etransfer_recon(file_path)

        if recon_result['success']:
            logger.info(f"eTransfer Recon for {recon_result['file_name']} completed successfully.")

            # Log validation summary if available
            if recon_result['validation_summary']:
                summary = recon_result['validation_summary']
                logger.info(f"Validation Summary: {summary['valid_transactions']}/{summary['total_transactions']} transactions valid")

                if not summary['valid']:
                    logger.warning("File contains invalid transactions:")
                    for invalid_tx in summary['invalid_transactions']:
                        logger.warning(f"  Transaction {invalid_tx['transaction_index']}: {invalid_tx['errors']}")
        else:
            logger.error(f"eTransfer Recon for {recon_result['file_name']} failed:")
            increment("s3.file_processed.failure", tags=[f"filename:{recon_result['file_name']}"])
            for error in recon_result['errors']:
                logger.error(f"  - {error}")
    finally:
        # Clean up temp file if S3 was used
        if args.s3_bucket and args.s3_key and file_path:
            try:
                shutil.rmtree(os.path.dirname(file_path))
            except Exception:
                pass

if __name__ == "__main__":
    init_datadog()
    main()