import os
import psycopg2
import logging
from typing import Optional, Dict, Any
from contextlib import contextmanager
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

logger = logging.getLogger("eTransfer_recon.utils.database")

class DatabaseConnection:
  """Database connection utility for PostgreSQL operations."""

  def __init__(self):
    self.connection_params = {
      'host': os.getenv('ACCOUNT_DB_HOST', 'localhost'),
      'port': os.getenv('ACCOUNT_DB_PORT', '5432'),
      'database': os.getenv('ACCOUNT_DB_NAME', 'postgres'),
      'user': os.getenv('ACCOUNT_DB_USERNAME', 'postgres'),
      'password': os.getenv('ACCOUNT_DB_PASSWORD', 'postgres'),
      'options': f"-c search_path={os.getenv('ACCOUNT_DB_SCHEMA', 'ledger_account')}"
    }

  @contextmanager
  def get_connection(self):
    """Context manager for database connections."""
    conn = None
    try:
      conn = psycopg2.connect(**self.connection_params)
      yield conn
    except psycopg2.Error as e:
      logger.error(f"Database connection error: {e}")
      raise
    finally:
      if conn:
        conn.close()

  def validate_account_ref_id(self, account_ref_id: str) -> Dict[str, Any]:
    try:
      with self.get_connection() as conn:
        with conn.cursor() as cursor:
          # Query to check if account exists
          query = """
                        SELECT ref_id, account_name, status, created_date_time
                        FROM account 
                        WHERE ref_id = %s
                    """
          cursor.execute(query, (account_ref_id,))
          result = cursor.fetchone()

          if result:
            return {
              'valid': True,
              'account_ref_id': result[0],
              'account_name': result[1],
              'status': result[2],
              'created_date_time': result[3]
            }
          else:
            return {
              'valid': False,
              'account_ref_id': account_ref_id,
              'error': 'Account not found'
            }

    except psycopg2.Error as e:
      logger.error(f"Database query error: {e}")
      return {
        'valid': False,
        'account_ref_id': account_ref_id,
        'error': f'Database error: {str(e)}'
      }
    except Exception as e:
      logger.error(f"Unexpected error during account validation: {e}")
      return {
        'valid': False,
        'account_ref_id': account_ref_id,
        'error': f'Unexpected error: {str(e)}'
      }

# Global database connection instance
db_connection = DatabaseConnection()