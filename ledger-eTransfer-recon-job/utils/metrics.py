import os
import logging
from datadog import initialize, statsd

logger = logging.getLogger("eTransfer_recon.main")
logging.basicConfig(level=logging.INFO)

# Extract environment info with sensible defaults
ENV = os.getenv("ENV", "staging")
DOMAIN = os.getenv("DOMAIN", "general-ledger")
SERVICE = os.getenv("SERVICE", "gl-recon-etransfer-batch-v1")
VERSION = os.getenv("VERSION", "latest")

def init_datadog():
  """
  Initialize the Datadog statsd client with detailed diagnostic logging.
  """
  logger.info("--- Starting Datadog Initialization ---")

  try:
    # Step 1: Log the environment variables we are using
    dd_agent_host = os.getenv("DD_AGENT_HOST")
    dd_dogstatsd_url = os.getenv("DD_DOGSTATSD_URL", "unix:///var/run/datadog/dsd.socket")

    logger.info(f"DD_AGENT_HOST environment variable: {dd_agent_host}")
    logger.info(f"DD_DOGSTATSD_URL environment variable: {dd_dogstatsd_url}")

    # Step 2: Determine the final socket path
    socket_path = dd_dogstatsd_url.removeprefix("unix://")
    logger.info(f"Resolved DogStatsD socket path: {socket_path}")

    # Step 3: Check if the directory and socket file exist (CRITICAL DEBUG STEP)
    socket_dir = os.path.dirname(socket_path)
    if os.path.exists(socket_dir):
      logger.info(f"SUCCESS: The directory '{socket_dir}' exists.")
      try:
        # List contents to see if the socket is there
        dir_contents = os.listdir(socket_dir)
        logger.info(f"Contents of '{socket_dir}': {dir_contents}")
        if not dir_contents:
          logger.warning("The socket directory exists but is EMPTY. Is the Datadog Agent running on this node?")
      except Exception as e:
        logger.error(f"Could not list contents of '{socket_dir}': {e}")
    else:
      logger.error(f"CRITICAL FAILURE: The directory '{socket_dir}' does NOT exist inside the container.")
      logger.error("This is the most common cause of failure. Check that the Kubernetes 'volumeMounts' are correctly configured and that the Datadog Agent is running on the node.")

    if os.path.exists(socket_path):
      logger.info(f"SUCCESS: The socket file '{socket_path}' exists.")
    else:
      logger.warning(f"FAILURE: The socket file '{socket_path}' does NOT exist inside the container.")

    # Step 4: Attempt to initialize and send a metric
    logger.info("Attempting to initialize Datadog client...")
    initialize(
        statsd_namespace=DOMAIN,
        statsd_socket_path=socket_path,
    )
    logger.info("Datadog client initialized. Attempting to send startup metric...")

    statsd.increment("service.started", tags=get_env_tags())

    logger.info("--- Datadog Initialization Successful ---")

  except Exception as e:
    logger.error(f"--- Datadog Initialization FAILED ---")
    logger.error(f"An exception occurred during Datadog setup: {e}", exc_info=True)

def get_env_tags(extra_tags=None):
  """
  Return consistent Datadog environment tags, optionally appending more.
  """
  base_tags = [
    f"env:{ENV}",
    f"service:{SERVICE}",
    f"version:{VERSION}",
  ]
  return base_tags + (extra_tags or [])

def increment(metric_name, tags=None):
  """
  Increment a Datadog counter with environment tags.
  """
  statsd.increment(metric_name, tags=get_env_tags(tags))

def gauge(metric_name, value, tags=None):
  """
  Send a gauge metric with environment tags.
  """
  statsd.gauge(metric_name, value, tags=get_env_tags(tags))

def timing(metric_name, value, tags=None):
  """
  Send a timing metric with environment tags.
  """
  statsd.timing(metric_name, value, tags=get_env_tags(tags))

def event(title, text, alert_type="info", tags=None):
  """
  Send a Datadog event with environment tags.
  """
  statsd.event(title, text, alert_type=alert_type, tags=get_env_tags(tags))

def count(metric_name, value=1, tags=None):
  """
  Send a count metric with environment tags.
  """
  statsd.count(metric_name, value, tags=get_env_tags(tags))

def histogram(metric_name, value, tags=None):
  """
  Send a histogram metric with environment tags.
  """
  statsd.histogram(metric_name, value, tags=get_env_tags(tags))

def track_file_processing_metrics(file_name, processing_result):
  """
  Track comprehensive file processing metrics.
  """
  base_tags = [f"filename:{file_name}"]

  # File processing status
  if processing_result['success']:
    if processing_result.get('validation_summary', {}).get('valid', True):
      increment("recon.file.processing.success", tags=base_tags)
    else:
      increment("recon.file.processing.success_with_issues", tags=base_tags)
  else:
    increment("recon.file.processing.failure", tags=base_tags)

  # Transaction counts if validation summary available
  if processing_result.get('validation_summary'):
    summary = processing_result['validation_summary']

    # Transaction counts
    count("recon.transaction.processed", summary['total_transactions'], tags=base_tags)
    count("recon.transaction.valid", summary['valid_transactions'], tags=base_tags)
    count("recon.transaction.invalid", len(summary['invalid_transactions']), tags=base_tags)

    # Success rate
    if summary['total_transactions'] > 0:
      success_rate = (summary['valid_transactions'] / summary['total_transactions']) * 100
      gauge("recon.transaction.success_rate_percent", success_rate, tags=base_tags)

    # Track validation issues by type
    if summary['invalid_transactions']:
      error_types = {}
      for invalid_tx in summary['invalid_transactions']:
        for error in invalid_tx['errors']:
          # Extract error type from error message
          if "Missing" in error:
            error_type = "missing_field"
          elif "Invalid" in error:
            error_type = "invalid_value"
          elif "amount" in error.lower():
            error_type = "amount_validation"
          elif "currency" in error.lower():
            error_type = "currency_validation"
          elif "timestamp" in error.lower():
            error_type = "timestamp_validation"
          else:
            error_type = "other_validation"

          error_types[error_type] = error_types.get(error_type, 0) + 1

      # Send counts for each error type
      for error_type, count_val in error_types.items():
        count(f"recon.validation.error.{error_type}", count_val, tags=base_tags)

def track_validation_error_details(file_name, invalid_transactions):
  """
  Track detailed validation error metrics for troubleshooting.
  """
  base_tags = [f"filename:{file_name}"]

  for invalid_tx in invalid_transactions:
    tx_tags = base_tags + [
      f"transaction_index:{invalid_tx['transaction_index']}",
      f"entry_ref:{invalid_tx.get('entry_ref', 'unknown')}"
    ]

    # Count errors per transaction
    count("recon.transaction.error_count", len(invalid_tx['errors']), tags=tx_tags)

    # Track specific field errors
    for error in invalid_tx['errors']:
      if "EntryRef" in error:
        increment("recon.validation.error.entry_ref", tags=tx_tags)
      elif "amount" in error.lower():
        increment("recon.validation.error.amount", tags=tx_tags)
      elif "currency" in error.lower():
        increment("recon.validation.error.currency", tags=tx_tags)
      elif "credit/debit" in error.lower():
        increment("recon.validation.error.credit_debit", tags=tx_tags)
      elif "status" in error.lower():
        increment("recon.validation.error.status", tags=tx_tags)
      elif "EndToEndId" in error:
        increment("recon.validation.error.end_to_end_id", tags=tx_tags)
      elif "timestamp" in error.lower():
        increment("recon.validation.error.timestamp", tags=tx_tags)
      else:
        increment("recon.validation.error.other", tags=tx_tags)