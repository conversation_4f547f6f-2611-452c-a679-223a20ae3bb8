import os
import sys
import pytest
from lxml import etree

# Add the job directory to sys.path for import
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))
from validation.transaction import validate_transaction_fields

TEST_DATA_DIR = os.path.join(os.path.dirname(__file__), 'data')

@pytest.fixture(scope="module", autouse=True)
def setup_env():
    os.environ['GL_ACCOUNT_ID'] = 'a602be21-1770-41ff-9788-a0ddd2bdeaad'

def parse_xml(xml_path):
  with open(xml_path, 'rb') as f:
    return etree.parse(f)

def test_validate_transaction_fields_invalid_currency():
    """Test case for invalid currency in a transaction"""

    # Update the XML path to point to the correct XML file
    xml_path = os.path.join(TEST_DATA_DIR, 'mixed_valid_invalid_transaction.xml')  # Correct file name
    doc = parse_xml(xml_path)
    result = validate_transaction_fields(doc)

    # Assert total number of transactions
    assert result['total_transactions'] == 2  # Only 2 transactions left

    # Assert valid transactions: expecting 1, since Transaction 2 has invalid currency
    assert result['valid_transactions'] == 1  # Expecting 1 valid transaction

    # Overall validation should be False due to the invalid currency in Transaction 2
    assert result['valid'] is False

    # Check the details of the invalid transaction (Transaction 2 in this case)
    invalid_tx = result['invalid_transactions'][0]  # Transaction 2 should be invalid due to currency
    assert invalid_tx['transaction_index'] == 2  # Transaction 2 is invalid
    assert invalid_tx['entry_ref'] == 'entry-ref-002'
    assert invalid_tx['currency'] == 'rrrr'  # Invalid currency type
    assert len(invalid_tx['errors']) > 0  # Ensure there are error messages

    # Check for specific error message regarding the invalid currency
    assert "Invalid or missing currency (must be CAD): rrrr" in invalid_tx['errors'][0]

def test_validate_transaction_fields_structure():

    xml_path = os.path.join(TEST_DATA_DIR, 'mixed_valid_invalid_transaction.xml')  # Correct file name
    doc = parse_xml(xml_path)
    result = validate_transaction_fields(doc)

    # Verify result structure
    assert 'valid' in result
    assert 'total_transactions' in result
    assert 'valid_transactions' in result
    assert 'invalid_transactions' in result
    assert 'errors' in result

    # Verify data types
    assert isinstance(result['valid'], bool)
    assert isinstance(result['total_transactions'], int)
    assert isinstance(result['valid_transactions'], int)
    assert isinstance(result['invalid_transactions'], list)
    assert isinstance(result['errors'], list)

    # Verify invalid transaction structure
    if len(result['invalid_transactions']) > 0:
        invalid_tx = result['invalid_transactions'][0]
        assert 'transaction_index' in invalid_tx
        assert 'entry_ref' in invalid_tx
        assert 'end_to_end_id' in invalid_tx
        assert 'amount' in invalid_tx
        assert 'currency' in invalid_tx
        assert 'credit_debit_indicator' in invalid_tx
        assert 'status' in invalid_tx
        assert 'errors' in invalid_tx
