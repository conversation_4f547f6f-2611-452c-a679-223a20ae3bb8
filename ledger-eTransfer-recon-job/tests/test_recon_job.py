import os
import sys
import pytest
from lxml import etree

# Add the job directory to sys.path for import
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))
from validation.file_name import validate_file_name
from validation.header import parse_and_validate_headers
from validation.transaction import validate_transaction_fields

TEST_DATA_DIR = os.path.join(os.path.dirname(__file__), 'data')
VALID_XML = 'etransfer_gl_recon_a602be21-1770-41ff-9788-a0ddd2bdeaad_20250714.xml'

@pytest.fixture(scope="module", autouse=True)
def setup_env():
    os.environ['GL_ACCOUNT_ID'] = 'a602be21-1770-41ff-9788-a0ddd2bdeaad'

def parse_xml(xml_path):
    with open(xml_path, 'rb') as f:
        return etree.parse(f)

def test_validate_file_name_valid():
    file_path = os.path.join(TEST_DATA_DIR, VALID_XML)
    assert validate_file_name(file_path) is True

def test_validate_file_name_invalid():
    file_path = os.path.join(TEST_DATA_DIR, 'invalid_file_name.xml')
    with open(file_path, 'w') as f:
        f.write('dummy')
    assert validate_file_name(file_path) is False
    os.remove(file_path)

def test_parse_and_validate_headers_valid():
    xml_path = os.path.join(TEST_DATA_DIR, VALID_XML)
    doc = parse_xml(xml_path)
    assert parse_and_validate_headers(doc) is True

def test_parse_and_validate_headers_invalid():
    xml_path = os.path.join(TEST_DATA_DIR, 'invalid_header.xml')
    doc = parse_xml(xml_path)
    assert parse_and_validate_headers(doc) is False

def test_validate_transaction_fields_valid():
    xml_path = os.path.join(TEST_DATA_DIR, VALID_XML)
    doc = parse_xml(xml_path)
    result = validate_transaction_fields(doc)
    assert result['valid'] is True
    assert result['total_transactions'] > 0
    assert result['valid_transactions'] == result['total_transactions']
    assert len(result['invalid_transactions']) == 0

def test_validate_transaction_fields_invalid():
    xml_path = os.path.join(TEST_DATA_DIR, 'invalid_transaction.xml')
    doc = parse_xml(xml_path)
    result = validate_transaction_fields(doc)
    assert result['valid'] is False
    assert result['total_transactions'] > 0
    assert len(result['invalid_transactions']) > 0
    assert len(result['errors']) > 0

def test_validate_transaction_fields_continues_processing():
    """Test that validation continues processing even when invalid transactions are found"""
    xml_path = os.path.join(TEST_DATA_DIR, 'invalid_transaction.xml')
    doc = parse_xml(xml_path)
    result = validate_transaction_fields(doc)

    # Should return results even with invalid transactions
    assert 'valid' in result
    assert 'total_transactions' in result
    assert 'valid_transactions' in result
    assert 'invalid_transactions' in result
    assert 'errors' in result

    # Should contain detailed information about invalid transactions
    if len(result['invalid_transactions']) > 0:
        invalid_tx = result['invalid_transactions'][0]
        assert 'transaction_index' in invalid_tx
        assert 'errors' in invalid_tx
        assert len(invalid_tx['errors']) > 0

def test_validate_transaction_fields_mixed_valid_invalid():
    """Test that validation continues processing when only some transactions are invalid"""
    xml_path = os.path.join(TEST_DATA_DIR, 'mixed_valid_invalid_transaction.xml')
    doc = parse_xml(xml_path)
    result = validate_transaction_fields(doc)

    # Should have 3 total transactions
    assert result['total_transactions'] == 3

    # Should have 2 valid and 1 invalid transaction
    assert result['valid_transactions'] == 2
    assert len(result['invalid_transactions']) == 1

    # Overall validation should be False due to invalid transaction
    assert result['valid'] is False

    # Should contain detailed information about the invalid transaction
    invalid_tx = result['invalid_transactions'][0]
    assert invalid_tx['transaction_index'] == 2  # Transaction 2 is invalid
    assert invalid_tx['entry_ref'] == 'entry-ref-002'
    assert invalid_tx['amount'] == 0.0  # Zero amount causes validation failure
    assert len(invalid_tx['errors']) > 0

    # Should have error messages
    assert len(result['errors']) > 0