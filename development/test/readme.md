# (Install dependencies:)
pip install -r requirements.txt

# Use default number of calls from environment file
python ledger_load_test.py --env qas

# Specify custom number of calls for QAS
python ledger_load_test.py --env qas 5000 --display 1

# Run 100 calls on staging
python3 ledger_load_test.py --env stg 100 --display 1

# Run 10000 calls on staging (override the env file default)
python ledger_load_test.py --env stg 10000 --display 1

# Default environment (qas) with custom calls
python ledger_load_test.py 2000 