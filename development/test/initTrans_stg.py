import json
import requests
from requests_aws4auth import <PERSON><PERSON><PERSON><PERSON><PERSON>
from datetime import datetime, timedelta
import uuid
import time
import random
import string
import sys

def get_random_word(length):
  letters = string.ascii_lowercase
  return ''.join(random.choice(letters) for _ in range(length))

service = 'execute-api'
region = 'ca-central-1'
url = 'https://ledger-stg-api.peoplescloud.io/internal/v1/ledger/transaction'
aws_access_key_id = '********************'
aws_secret_access_key = '2U70kvR/Fy+XDI30PKiOiRzKsNyIwguwuFaNYJdT'
aws_auth = AWS4Auth(aws_access_key_id, aws_secret_access_key, region, service)

headers = {
  'x-pg-interaction-id': 'a32a107c-63d9-4de2-b268-dc39fb0b3dde',
  'x-pg-profile-id': '6b75b999-069e-4048-9bac-fe4f28a08ba0',
  'x-pg-account-id': '2861245b-d25e-417b-adce-2bc8777147c5',
  'Content-Type': 'application/json'
}

total_time_taken = 0
num_calls = 1000
over_500ms_count = 0
success_count = 0
# variables for min and max call times
min_call_time = sys.float_info.max  # Initialized with the maximum float value
max_call_time = 0.0  # Initialized with 0

for i in range(num_calls):
  current_time = datetime.utcnow()

  payload = {
    "instruction_ref_id": get_random_word(5) + str(random.randint(10000,99999)),
    "payment_rail": "ETRANSFER",
    "transactions": [
      {
        "transaction_ref_id": str(uuid.uuid4()),
        "payment_category": "FULFILL_REQUEST_FOR_PAYMENT",
        "amount": round(random.uniform(1, 100), 2),
        "monetary_unit": "CAD",
        "acceptance_date_time": current_time.isoformat()+'Z',
        "due_date_time": (current_time + timedelta(days=1)).isoformat()+'Z'
      },
      {
        "transaction_ref_id": str(uuid.uuid4()),
        "payment_category": "FULFILL_REQUEST_FOR_PAYMENT",
        "amount": round(random.uniform(1, 100), 2),
        "monetary_unit": "CAD",
        "acceptance_date_time": current_time.isoformat()+'Z',
        "due_date_time": (current_time + timedelta(days=1)).isoformat()+'Z'
      },
      {
        "transaction_ref_id": str(uuid.uuid4()),
        "payment_category": "SEND_PAYMENT",
        "amount": round(random.uniform(1, 100), 2),
        "monetary_unit": "CAD",
        "acceptance_date_time": current_time.isoformat()+'Z',
        "due_date_time": (current_time + timedelta(days=1)).isoformat()+'Z'
      }
    ]
  }

  headers['x-pg-interaction-timestamp'] = current_time.isoformat()+'Z'

  try:
    start_time = time.time()

    response = requests.post(url, auth=aws_auth, data=json.dumps(payload), headers=headers)

    end_time = time.time()

    if response.status_code == 201:
      success_count += 1

    time_taken = (end_time - start_time) * 1000  # convert time to milliseconds

    total_time_taken += time_taken

    # update min and max call times
    min_call_time = min(min_call_time, time_taken)
    max_call_time = max(max_call_time, time_taken)

    if time_taken > 500:
      over_500ms_count += 1

    # print(f"Call #{i+1} Status Code: {response.status_code}, Time: {time_taken} ms, Response: {response.text}")
    if (i + 1) % 10 == 0:  # check if it's the 10th call
      current_time = datetime.now().strftime("%Y-%m-%d %H:%M:%S")  # format the timestamp
      print(f"Call #{i+1} Status Code: {response.status_code}, Time: {time_taken} ms, Timestamp: {current_time}")

  except Exception as e:
    print(f"Call #{i+1} encountered an error: {e}")

  time.sleep(random.randint(10, 30) / 1000)  # adding delay between 10 to 30 milliseconds

avg_time_taken = total_time_taken / num_calls

print(f"Average call time: {avg_time_taken} ms")
print(f"Number of calls with time over 500ms: {over_500ms_count}")
print(f"Number of successful calls: {success_count}")
# Print min and max call times at the end
print(f"Min Call Time: {min_call_time} ms")
print(f"Max Call Time: {max_call_time} ms")