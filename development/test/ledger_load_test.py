import json
import requests
from requests_aws4auth import <PERSON><PERSON><PERSON><PERSON><PERSON>
from datetime import datetime, timedelta
import uuid
import time
import random
import string
import sys
import os
from dotenv import load_dotenv
import argparse
from concurrent.futures import Thread<PERSON>oolExecutor, as_completed
from threading import Lock
import statistics

# Lock for thread-safe printing and metrics updates
print_lock = Lock()
metrics_lock = Lock()

def get_random_word(length):
  """Generate a random word of specified length."""
  letters = string.ascii_lowercase
  return ''.join(random.choice(letters) for _ in range(length))

def load_environment(env_name='qas'):
  """Load environment variables from the specified .env file."""
  env_file = f'.env.{env_name}'
  if not os.path.exists(env_file):
    print(f"Error: Environment file {env_file} not found!")
    sys.exit(1)

  load_dotenv(env_file, override=True)
  print(f"Loaded environment: {env_name}")

  # Validate required environment variables
  required_vars = [
    'API_URL', 'AWS_ACCESS_KEY_ID', 'AWS_SECRET_ACCESS_KEY',
    'PROFILE_ID', 'ACCOUNT_ID'
  ]

  missing_vars = [var for var in required_vars if not os.getenv(var)]
  if missing_vars:
    print(f"Error: Missing required environment variables: {', '.join(missing_vars)}")
    sys.exit(1)

def make_api_call(call_number, url, aws_auth, base_headers, total_calls, display_interval):
  """Make a single API call and return metrics."""
  current_time = datetime.utcnow()

  # Generate a unique interaction ID for this specific call
  call_interaction_id = str(uuid.uuid4())

  # Create headers for this call with unique interaction ID
  headers = base_headers.copy()
  headers['x-pg-interaction-id'] = call_interaction_id
  headers['x-pg-interaction-timestamp'] = current_time.isoformat() + 'Z'

  # Build payload
  payload = {
    "instruction_ref_id": get_random_word(5) + str(random.randint(10000, 99999)),
    "payment_rail": "ETRANSFER",
    "transactions": [
      {
        "transaction_ref_id": str(uuid.uuid4()),
        "payment_category": "FULFILL_REQUEST_FOR_PAYMENT",
        "amount": round(random.uniform(1, 100), 2),
        "monetary_unit": "CAD",
        "acceptance_date_time": current_time.isoformat() + 'Z',
        "due_date_time": (current_time + timedelta(days=1)).isoformat() + 'Z'
      },
      {
        "transaction_ref_id": str(uuid.uuid4()),
        "payment_category": "FULFILL_REQUEST_FOR_PAYMENT",
        "amount": round(random.uniform(1, 100), 2),
        "monetary_unit": "CAD",
        "acceptance_date_time": current_time.isoformat() + 'Z',
        "due_date_time": (current_time + timedelta(days=1)).isoformat() + 'Z'
      },
      {
        "transaction_ref_id": str(uuid.uuid4()),
        "payment_category": "SEND_PAYMENT",
        "amount": round(random.uniform(1, 100), 2),
        "monetary_unit": "CAD",
        "acceptance_date_time": current_time.isoformat() + 'Z',
        "due_date_time": (current_time + timedelta(days=1)).isoformat() + 'Z'
      }
    ]
  }

  try:
    start_time = time.time()
    response = requests.post(url, auth=aws_auth, data=json.dumps(payload), headers=headers)
    end_time = time.time()

    time_taken = (end_time - start_time) * 1000  # convert to milliseconds

    # Print if it matches the display interval
    if call_number % display_interval == 0 or display_interval == 1:
      with print_lock:
        current_time_str = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        print(f"Call #{call_number}/{total_calls} Status Code: {response.status_code}, "
              f"Time: {time_taken:.2f} ms, Timestamp: {current_time_str}")

    return {
      'call_number': call_number,
      'status_code': response.status_code,
      'time_taken': time_taken,
      'success': response.status_code == 201,
      'error': None
    }

  except Exception as e:
    with print_lock:
      print(f"Call #{call_number} encountered an error: {e}")
    return {
      'call_number': call_number,
      'status_code': None,
      'time_taken': 0,
      'success': False,
      'error': str(e)
    }

def main(env_name='qas', num_calls=None, concurrent_calls=10, display_interval=10):
  """Main function to run the load test."""
  # Load environment configuration
  load_environment(env_name)

  # AWS Configuration
  service = 'execute-api'
  region = 'ca-central-1'
  url = os.getenv('API_URL')
  aws_access_key_id = os.getenv('AWS_ACCESS_KEY_ID')
  aws_secret_access_key = os.getenv('AWS_SECRET_ACCESS_KEY')
  aws_auth = AWS4Auth(aws_access_key_id, aws_secret_access_key, region, service)

  # Base headers configuration (without interaction ID)
  base_headers = {
    'x-pg-profile-id': os.getenv('PROFILE_ID'),
    'x-pg-account-id': os.getenv('ACCOUNT_ID'),
    'Content-Type': 'application/json'
  }

  # Test configuration - use command line argument if provided, otherwise use env file
  if num_calls is None:
    num_calls = int(os.getenv('DEFAULT_NUM_CALLS', 1000))

  # Metrics initialization
  all_times = []
  success_count = 0
  failed_calls = []
  over_500ms_count = 0

  print(f"Starting load test for {env_name.upper()} environment")
  print(f"URL: {url}")
  print(f"Total calls to make: {num_calls}")
  print(f"Concurrent calls: {concurrent_calls}")
  print(f"Display interval: Every {display_interval} call(s)")
  print("-" * 50)

  start_test_time = time.time()

  if concurrent_calls == 1:
    # Sequential execution for better ordered output
    for i in range(num_calls):
      current_time = datetime.utcnow()

      # Generate a unique interaction ID for this specific call
      call_interaction_id = str(uuid.uuid4())

      # Create headers for this call with unique interaction ID
      headers = base_headers.copy()
      headers['x-pg-interaction-id'] = call_interaction_id
      headers['x-pg-interaction-timestamp'] = current_time.isoformat() + 'Z'

      # Build payload
      payload = {
        "instruction_ref_id": get_random_word(5) + str(random.randint(10000, 99999)),
        "payment_rail": "ETRANSFER",
        "transactions": [
          {
            "transaction_ref_id": str(uuid.uuid4()),
            "payment_category": "FULFILL_REQUEST_FOR_PAYMENT",
            "amount": round(random.uniform(1, 100), 2),
            "monetary_unit": "CAD",
            "acceptance_date_time": current_time.isoformat() + 'Z',
            "due_date_time": (current_time + timedelta(days=1)).isoformat() + 'Z'
          },
          {
            "transaction_ref_id": str(uuid.uuid4()),
            "payment_category": "FULFILL_REQUEST_FOR_PAYMENT",
            "amount": round(random.uniform(1, 100), 2),
            "monetary_unit": "CAD",
            "acceptance_date_time": current_time.isoformat() + 'Z',
            "due_date_time": (current_time + timedelta(days=1)).isoformat() + 'Z'
          },
          {
            "transaction_ref_id": str(uuid.uuid4()),
            "payment_category": "SEND_PAYMENT",
            "amount": round(random.uniform(1, 100), 2),
            "monetary_unit": "CAD",
            "acceptance_date_time": current_time.isoformat() + 'Z',
            "due_date_time": (current_time + timedelta(days=1)).isoformat() + 'Z'
          }
        ]
      }

      try:
        start_time = time.time()
        response = requests.post(url, auth=aws_auth, data=json.dumps(payload), headers=headers)
        end_time = time.time()

        time_taken = (end_time - start_time) * 1000  # convert to milliseconds

        if response.status_code == 201:
          success_count += 1
        else:
          failed_calls.append((i+1, response.status_code))

        all_times.append(time_taken)

        if time_taken > 500:
          over_500ms_count += 1

        # Print based on display interval
        if (i + 1) % display_interval == 0 or display_interval == 1:
          current_time_str = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
          print(f"Call #{i+1}/{num_calls} Status Code: {response.status_code}, "
                f"Time: {time_taken:.2f} ms, Timestamp: {current_time_str}")

      except Exception as e:
        print(f"Call #{i+1} encountered an error: {e}")
        failed_calls.append((i+1, str(e)))

      # Add delay between calls
      time.sleep(random.randint(10, 30) / 1000)

  else:
    # Concurrent execution
    with ThreadPoolExecutor(max_workers=concurrent_calls) as executor:
      # Submit all tasks
      futures = []
      for i in range(num_calls):
        future = executor.submit(make_api_call, i+1, url, aws_auth, base_headers, num_calls, display_interval)
        futures.append(future)

        # Add a small delay between submissions to avoid overwhelming
        time.sleep(random.randint(1, 5) / 1000)  # 1-5ms delay

      # Process completed futures
      for future in as_completed(futures):
        result = future.result()

        if result['error']:
          failed_calls.append((result['call_number'], result['error']))
        else:
          if result['success']:
            success_count += 1
          else:
            failed_calls.append((result['call_number'], result['status_code']))

          all_times.append(result['time_taken'])

          if result['time_taken'] > 500:
            over_500ms_count += 1

  end_test_time = time.time()
  total_test_time = end_test_time - start_test_time

  # Calculate statistics
  if all_times:
    avg_time = statistics.mean(all_times)
    min_time = min(all_times)
    max_time = max(all_times)
    median_time = statistics.median(all_times)

    # Calculate percentiles
    sorted_times = sorted(all_times)
    p95_index = int(len(sorted_times) * 0.95)
    p99_index = int(len(sorted_times) * 0.99)
    p95_time = sorted_times[p95_index] if p95_index < len(sorted_times) else max_time
    p99_time = sorted_times[p99_index] if p99_index < len(sorted_times) else max_time
  else:
    avg_time = min_time = max_time = median_time = p95_time = p99_time = 0

  # Calculate throughput
  actual_throughput = num_calls / total_test_time if total_test_time > 0 else 0

  # Print summary statistics
  print("\n" + "=" * 50)
  print(f"LOAD TEST SUMMARY - {env_name.upper()}")
  print("=" * 50)
  print(f"Test Configuration:")
  print(f"  Concurrent calls: {concurrent_calls}")
  print(f"  Total calls made: {num_calls}")
  print(f"  Total test duration: {total_test_time:.2f} seconds")
  print(f"  Actual throughput: {actual_throughput:.2f} calls/second")

  print(f"\nResults:")
  print(f"  Successful calls: {success_count}")
  print(f"  Failed calls: {num_calls - success_count}")
  print(f"  Success rate: {(success_count/num_calls)*100:.2f}%")

  print(f"\nPerformance Metrics:")
  print(f"  Average response time: {avg_time:.2f} ms")
  print(f"  Median response time: {median_time:.2f} ms")
  print(f"  Min response time: {min_time:.2f} ms")
  print(f"  Max response time: {max_time:.2f} ms")
  print(f"  95th percentile: {p95_time:.2f} ms")
  print(f"  99th percentile: {p99_time:.2f} ms")
  print(f"  Calls over 500ms: {over_500ms_count} ({(over_500ms_count/num_calls)*100:.2f}%)")

  if failed_calls and len(failed_calls) <= 10:
    print(f"\nFailed calls details (showing first 10):")
    for call_num, status in failed_calls[:10]:
      print(f"  Call #{call_num}: Status {status}")

if __name__ == "__main__":
  parser = argparse.ArgumentParser(
      description='Run ledger load test for different environments',
      formatter_class=argparse.RawDescriptionHelpFormatter,
      epilog="""
Examples:
  python ledger_load_test.py --env qas                      # Default: every 10th call
  python ledger_load_test.py --env stg 500 --display 1      # Show every call
  python ledger_load_test.py --env qas 1000 --display 50    # Show every 50th call
  python ledger_load_test.py --env stg 100 --concurrent 10 --display 1  # Concurrent with every call shown
  python ledger_load_test.py --env qas 5000 --display 100   # Show every 100th call
        """
  )

  parser.add_argument('--env',
                      type=str,
                      default='qas',
                      choices=['qas', 'stg'],
                      help='Environment to test (qas or stg)')

  parser.add_argument('num_calls',
                      type=int,
                      nargs='?',
                      default=None,
                      help='Number of calls to make (overrides env file setting)')

  parser.add_argument('--concurrent',
                      type=int,
                      default=10,
                      help='Number of concurrent calls (default: 1 for sequential)')

  parser.add_argument('--display',
                      type=int,
                      default=10,
                      help='Display every Nth call (default: 10, use 1 to show all)')

  args = parser.parse_args()

  # Validate inputs
  if args.num_calls is not None and args.num_calls <= 0:
    print("Error: Number of calls must be a positive integer")
    sys.exit(1)

  if args.concurrent <= 0:
    print("Error: Number of concurrent calls must be a positive integer")
    sys.exit(1)

  if args.display <= 0:
    print("Error: Display interval must be a positive integer")
    sys.exit(1)

  main(args.env, args.num_calls, args.concurrent, args.display)
