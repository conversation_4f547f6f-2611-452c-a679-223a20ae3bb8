apiVersion: apps/v1
kind: Deployment
metadata:
  name: account-deployment
  namespace: pg-ledger-staging
  labels:
    app: account-v1
    domain: general-ledger
    tags.datadoghq.com/env: staging
    tags.datadoghq.com/service: gl-account-v1
    tags.datadoghq.com/version: "**************"
spec:
  replicas: 3
  strategy:
    type: RollingUpdate
    rollingUpdate:
      maxSurge: 1
      maxUnavailable: 0
  minReadySeconds: 45
  selector:
    matchLabels:
      app: account-v1
  template:
    metadata:
      annotations:
        admission.datadoghq.com/java-lib.version: latest
      labels:
        app: account-v1
        admission.datadoghq.com/enabled: "true"
        domain: general-ledger
        tags.datadoghq.com/env: staging
        tags.datadoghq.com/service: gl-account-v1
        tags.datadoghq.com/version: "**************"
    spec:
      terminationGracePeriodSeconds: 90
      containers:
        - name: account-container
          lifecycle:
            preStop:
              exec:
                command: ["/bin/sh", "-c", "sleep 45"]
          image: ************.dkr.ecr.ca-central-1.amazonaws.com/ledger-account:**************
          resources:
            requests:
              memory: "384Mi"
              cpu: "100m"
            limits:
              memory: "512Mi"
              cpu: "400m"
          ports:
            - containerPort: 8080
          # Startup probe has higher priority over the two other probe types. Until the Startup Probe succeeds, all the other Probes are disabled.
          startupProbe:
            httpGet:
              path: /actuator/health
              port: 8080
              scheme: HTTP
            initialDelaySeconds: 10
            timeoutSeconds: 2
            periodSeconds: 15
            successThreshold: 1
            failureThreshold: 20
          # Kubelet uses liveness probes to know when to restart a container. If the liveness probe fails, the kubelet kills the container, and the container is subjected to its restart policy.
          livenessProbe:
            httpGet:
              path: /actuator/health
              port: 8080
              scheme: HTTP
            initialDelaySeconds: 20
            timeoutSeconds: 30
            periodSeconds: 300
            successThreshold: 1
            failureThreshold: 3
          # Kubernetes makes sure the readiness probe passes before allowing a service to send traffic to the pod. Unlike a liveness probe, a readiness probe doesn’t kill the container. If the readiness probe fails, Kubernetes simply hides the container’s Pod from corresponding Services, so that no traffic is redirected to it.
          readinessProbe:
            httpGet:
              path: /actuator/health
              port: 8080
              scheme: HTTP
            initialDelaySeconds: 20
            timeoutSeconds: 2
            periodSeconds: 3
            successThreshold: 1
            failureThreshold: 3
          env:
            - name: TZ
              value: "America/Toronto"
            - name: DISABLE_COGNITO_JWT_VERIFICATION
              valueFrom:
                configMapKeyRef:
                  name: application-dependencies
                  key: app.jwt.token.check.disable
            - name: SPRING_PROFILES_ACTIVE
              value: "cloud"
            - name: ACCOUNT_WRITE_DB_HOST
              valueFrom:
                secretKeyRef:
                  name: account-database
                  key: sys.database.host
            - name: ACCOUNT_DB_SCHEMA
              valueFrom:
                secretKeyRef:
                  name: account-database
                  key: sys.database.schema
            - name: ACCOUNT_WRITE_DB_PORT
              value: "5432"
            - name: ACCOUNT_WRITE_DB_NAME
              valueFrom:
                secretKeyRef:
                  name: account-database
                  key: sys.database.name
            - name: ACCOUNT_WRITE_DB_USERNAME
              valueFrom:
                secretKeyRef:
                  name: account-database
                  key: sys.database.username
            - name: ACCOUNT_WRITE_DB_PASSWORD
              valueFrom:
                secretKeyRef:
                  name: account-database
                  key: sys.database.password
            - name: ACCOUNT_READ_DB_HOST
              valueFrom:
                secretKeyRef:
                  name: account-database
                  key: sys.database.read.host
            - name: ACCOUNT_READ_DB_PORT
              value: "5432"
            - name: ACCOUNT_READ_DB_NAME
              valueFrom:
                secretKeyRef:
                  name: account-database
                  key: sys.database.name
            - name: ACCOUNT_READ_DB_USERNAME
              valueFrom:
                secretKeyRef:
                  name: account-database
                  key: sys.database.username
            - name: ACCOUNT_READ_DB_PASSWORD
              valueFrom:
                secretKeyRef:
                  name: account-database
                  key: sys.database.password
            - name: PROFILE_API_URL
              valueFrom:
                configMapKeyRef:
                  name: application-dependencies
                  key: sys.profile.url
            - name: TRANSACTIONS_API_URL
              valueFrom:
                configMapKeyRef:
                  name: application-dependencies
                  key: sys.transaction.url
            - name: REDIS_DB_CLUSTER
              valueFrom:
                secretKeyRef:
                  name: redis-cache
                  key: sys.database.cluster
            - name: REDIS_READ_TIMEOUT
              valueFrom:
                configMapKeyRef:
                  name: application-dependencies
                  key: app.redis.read.timeout
            - name: REDIS_CONNECTION_TIMEOUT
              valueFrom:
                configMapKeyRef:
                  name: application-dependencies
                  key: app.redis.connection.timeout
            - name: MAXIMUM_POOL_SIZE
              valueFrom:
                configMapKeyRef:
                  name: application-dependencies
                  key: app.db.maximum.pool.size
            - name: DB_CONNECTION_TIME_OUT
              valueFrom:
                configMapKeyRef:
                  name: application-dependencies
                  key: app.db.connection.timeout
            - name: ACCOUNT_VALIDATION_SERVICE_CONNECTION_TIMEOUT
              valueFrom:
                configMapKeyRef:
                  name: application-dependencies
                  key: app.account.validation-service.timeout.connection
            - name: ACCOUNT_VALIDATION_SERVICE_READ_TIMEOUT
              valueFrom:
                configMapKeyRef:
                  name: application-dependencies
                  key: app.account.validation-service.timeout.read
            - name: LOG_LEVEL_ROOT
              valueFrom:
                configMapKeyRef:
                  name: application-dependencies
                  key: app.log.level.root
            - name: LOG_LEVEL_COM_PEOPLESTRUST
              valueFrom:
                configMapKeyRef:
                  name: application-dependencies
                  key: app.log.level.com.peoplestrust
            - name: LOG_LEVEL_HIBERNATE
              valueFrom:
                configMapKeyRef:
                  name: application-dependencies
                  key: app.log.level.hibernate
            - name: LOG_LEVEL_API_PAYLOAD_LOGGER
              valueFrom:
                configMapKeyRef:
                  name: application-dependencies
                  key: app.log.level.api.payload
            - name: LOG_LEVEL_PERF_LOGGER
              valueFrom:
                configMapKeyRef:
                  name: application-dependencies
                  key: app.log.level.perf
            - name: LOG_LEVEL_FLOW_LOGGER
              valueFrom:
                configMapKeyRef:
                  name: application-dependencies
                  key: app.log.level.flow
